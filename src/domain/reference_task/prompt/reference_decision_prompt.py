#!/usr/bin/env python3
"""
参考任务决策Prompt

基于参考成功案例的UI自动化测试决策prompt
参考decision_definition_prompt.py的编写风格和描述方式

Prompt块引用说明：
- get_reference_role_definition(): 角色定位 - 定义Agent的核心职责
- get_reference_test_case_description(): 测试用例信息 - 提供当前测试用例的详细信息
- _build_reference_actions_text(): 成功案例 - 提供参考的成功执行历史
- _build_current_execution_history(): 执行记忆 - 当前任务的执行记录
- get_image_element_extractor_prompt(): 图片元素提取器 - 指导如何从截图中提取相关元素
- get_interface_analysis_prompt(): 界面分析 - 指导如何分析当前界面
- get_reference_action_list(): 动作列表 - 可执行的动作类型和格式
- get_reference_learning_strategy(): 执行策略 - 如何学习和复用成功案例
- get_reference_exception_handling(): 特殊场景 - 处理各种异常情况的策略
- get_reference_action_decision_prompt(): 动作决策 - 决策流程和步骤
- get_reference_output_example(): 输出格式 - JSON输出格式要求
- get_reference_output_requirement(): 输出要求 - 输出的具体要求
- get_reference_execution_invoke_prompt(): 执行流程 - 整体执行流程指导
- get_reference_user_invoke_prompt(): 用户调用提示 - 最终的执行和输出要求
"""
import json
import re
from typing import Any

from src.domain.ui_task.mobile.repo.do.State import DeploymentState


def build_three_step_reference_decision_prompt(state: DeploymentState, current_step_description: str) -> tuple[
    str, str | Any]:
    """
    构建三步骤参考任务决策prompt，包含上一个、当前、下一个步骤的信息

    Args:
        state: 参考任务状态
        current_step_description: 当前步骤描述

    Returns:
        完整的系统prompt
    """
    # 获取步骤信息
    current_step_index = state.get("current_step_index", 0)
    task_steps = state.get("task_steps", [])

    # 构建三步骤测试用例信息
    three_step_test_case_info, current_step_name, next_step_name = get_three_step_test_case_description(state,
                                                                                                        current_step_index,
                                                                                                        task_steps)
    # 构建前一个步骤和当前步骤的执行记忆
    combined_execution_history = _build_combined_execution_history(
        state.get("history", [])
    )

    # 构建成功案例：按执行轮次顺序显示，包含当前步骤和下一步骤的成功案例
    combined_reference_text = _build_combined_reference_actions_text(
        state.get("reference_actions", []),
        current_step_description,
        task_steps[current_step_index + 1] if current_step_index + 1 < len(task_steps) else None,
        task_steps,
        current_step_index
    )

    thinking = "enabled" if '滑动' in current_step_name or '滚动' in current_step_name or '拖动' in current_step_name or '拖拽' in current_step_name else "disable"

    prompt = f"""
########## 角色定位 ##########
{get_three_step_reference_role_definition()}

########## 测试用例信息 ##########
{three_step_test_case_info}

########## 成功案例 ##########
**成功案例动态加载当前执行步骤全部执行记录以及下一步骤第一轮执行记录，当你切换到下一执行步骤时，成功案例会自动切换到下一步骤的第一轮执行记录**
{combined_reference_text}

########## 执行记忆 ##########
{combined_execution_history}

########## 界面分析 ##########
{get_interface_analysis_prompt()}

########## 动作列表 ##########
{get_reference_action_list()}

########## 特殊场景 ##########
{get_reference_exception_handling()}

########## 动作决策 ##########
{get_three_step_reference_action_decision_prompt(current_step_name, next_step_name)}

########## 输出格式 ##########
{get_three_step_output_example(thinking)}

########## 输出要求 ##########
{get_reference_output_requirement()}
"""
    print(prompt)
    return prompt, thinking


def get_three_step_reference_role_definition() -> str:
    """获取三步骤参考任务的角色定义"""
    return """你是一个资深安卓软件测试专家，正在执行回归测试，<当前轮截图>是手机最新截图，请你以<当前轮截图>为准，制定下一步执行动作
**核心职责**
认真负责，参照<成功案例>完成回归测试"""


def get_three_step_reference_action_decision_prompt(current_step_name: str, next_step_name: str = None) -> str:
    """获取三步骤参考动作决策提示，包含当前步骤和下一步骤信息"""

    return f"""**严格按照顺序执行动作决策**
1. **分析当前界面**
   - 调用<界面分析>对<当前轮截图>进行详细分析
   - 如果当前界面无法找到目标元素，可参考对应的<成功案例截图>确定元素位置
   - 如果界面包含<特殊场景>：参照<特殊场景>进行处理

2. **测试路径偏离检测与纠正**
   - 如果<当前轮截图>与<成功案例截图>均不相符，且与<执行记忆>没有关联，说明脱离了测试路径
   - 将<当前轮截图>、<执行记忆>、<成功案例>结合分析，制定返回正确测试路径的方案

3. **记录执行进度**
   - 读取<执行记忆><执行记忆界面截图>时刻记录着回归测试执行进度，继续推进回归测试
   - 确保按照测试用例顺序执行，不允许重复执行已经完成的步骤；不允许跳过未执行过的步骤

4. **切换执行步骤**
   * 当前正在执行步骤: {current_step_name}，下一执行步骤: {next_step_name}
   * 分析<执行记忆><当前轮截图><执行记忆界面截图>:
     - 如果当前步骤没有执行，则参照<成功案例><成功案例截图>执行当前步骤： current_step_name={current_step_name}
     - 如果当前步骤已经执行，结合<当前轮截图><执行记忆界面截图><成功案例截图><成功案例>判断是否已经完成，如果完成则切换到下一执行步骤：current_step_name={next_step_name}
   * 切换步骤后一定要将current_step_name更新，推进回归测试执行

5. **参考成功案例**
   * 获取最新current_step_name, current_step_name不需要操作界面(例如：等待、制定方案、理解等非操作界面动作)，则本次决策直接复用<成功案例>的'决策内容'、'操作指令'、'执行动作'
   * 获取最新current_step_name, 从<成功案例>中找到当前步骤的执行记录，成功案例包含了决策内容、决策动作以及当前操作的<成功案例截图>，参考文本内容以及<成功案例截图>红圈标记的元素，制定下一步执行动作

6. **用例结束判断**
   - **完成任务**：读取<执行记忆>和<测试用例信息>内容，所有步骤全部执行，必须执行完最后一步后，且满足期望结果，再调用finished
   - **终止条件**：分析<执行记忆>最近5轮执行记录,出现以下任一情况立即调用failed
     * 路径偏离：连续5轮无法返回正确测试路径
     * 进度停滞：连续5轮界面分析结果完全相同
     * 重复操作：连续5次执行相同动作且界面无变化
     * 异常循环：连续5次遇到相同异常且无法解决
   - **继续执行**：未达到上述任一终止条件，则继续执行"""


def get_three_step_test_case_description(state: DeploymentState, current_step_index: int, task_steps: list):
    """获取三步骤测试用例描述 - 显示全部步骤，标记当前执行的步骤，并返回下一步骤名称"""
    # 优先使用实际的测试用例名称，避免显示参考任务ID
    test_case_name = state.get("test_case_name", "未知测试用例")
    expected_result = state.get("expected_result", "")

    # 构建步骤信息，显示全部步骤
    step_info = ""
    current_step_name = ""
    next_step_name = None

    for i, step in enumerate(task_steps):
        if i == current_step_index:
            current_step_name = step
            step_info += f"**{step}(当前执行步骤)**\n"
            # 获取下一步骤名称
            if i + 1 < len(task_steps):
                next_step_name = task_steps[i + 1]
        else:
            # 其他步骤正常显示
            step_info += f"{step}\n"

    # 始终显示期望结果（如果存在）
    if expected_result:
        content = f"""- **用例名称**: {test_case_name}
- **用例步骤**:
{step_info}- **期望结果**: {expected_result}"""
    else:
        content = f"""- **用例名称**: {test_case_name}
- **用例步骤**:
{step_info}"""

    return content, current_step_name, next_step_name


def get_interface_analysis_prompt() -> str:
    """获取界面分析提示"""
    return """结合<当前轮截图>和<测试用例信息> 的'用例步骤'理解界面中图标、UI组件、文字内容和各元素的特征和位置信息
- 在界面中定位与<测试用例信息>的'用例步骤'的相关元素，必须严格按照'用例步骤'描述的**元素特征**和**位置信息**
- 优先根据 <测试用例信息>的'用例步骤'中描写的方位去聚焦并定位元素，要确定元素特征和位置的唯一性
- 禁止伪造、猜测不存在的元素和内容。"""


def get_reference_action_list() -> str:
    """获取参考任务的动作列表"""
    return """**必须补充动作参数，坐标必须使用标签<point>x1 y1</point>进行包裹**
1.click(point=<point>x1 y1</point>) # 必须补充点击位置坐标
2.long_press(point=<point>x1 y1</point>) # 必须补充长按位置坐标
3.type(content='text_to_input') # 输入文字内容，必须补充输入内容
4.scroll(point='<point>x1 y1</point>', direction='down or up or right or left') 
  - 滑动屏幕，必须补充滑动起点坐标，和direction方向参数
  - 滑动的方向direction就是屏幕被滑动位置移动的方向，向左滑动，页面元素向左移动
  - 由于屏幕是有连续加载性的，当我们向左滑动时，就会从屏幕右侧看到新的内容
5.drag(start_point='<point>x1 y1</point>', end_point='<point>x2 y2</point>') # 拖动元素，必须补充拖动起始坐标和结束坐标
6.wait(seconds=wait_seconds) # 等待，必须补充等待秒数
7.delete(content=delete_count) # 删除内容，必须补充删除文字数量
8.back() # 返回，用于返回上一级页面，或关闭弹窗
9.enter() # 回车，用于搜索框输入内容后直接搜索
10.print(content='output_content') # 专门用于输出全局执行路径、方案、计划内容
11.failed(content='reason') # 执行失败，必须补充失败原因
12.finished(content='success_message') # 执行成功，必须补充成功信息"""


def get_three_step_output_example(thinking: str) -> str:
    """获取三步骤模式的输出格式要求"""
    return f"""**严格按照以下JSON格式输出内容，禁止偷懒，严格按照要求输出每个字段内容**
{{
"interface_analysis": "简单描述<当前轮截图>的<界面分析>结果",
"action_decision": "{'详细' if thinking == 'enabled' else '简单'}描述<动作决策>结果，必须包含：1.说明用例进度以及下一步要做的内容 2.说明是否需要切换步骤 3.说明参考成功案例的内容和执行动作 4.如果遇到需要滑动的步骤，必须描述判断滑动到尽头的过程",
"current_step_name": "当前执行步骤，必须从<测试用例信息>获取步骤序号和描述，必须保证内容的准确性和一致性，不能添加任何其他内容；输出格式：1.点击登录按钮、2.输入用户名等",
"action": "纯净的动作名称和参数,必须引用<动作列表>中的动作，禁止编造<动作列表>中不存在的动作，输出格式：click(point=<point>x1 y1</point>)"
}}
"""


def get_reference_output_requirement() -> str:
    """获取输出要求"""
    return """严格遵循<输出格式>输出规定字段和对应内容，并保证使用JSON格式输出内容
输出的内容必须是JSON格式，方便后续动作及参数的解析和执行
    """


def system_invoke_prompt() -> str:
    return """############ 执行流程 ##########
1.你需要先进行<界面分析>理解<当前轮截图>元素和内容;
2.结合<界面分析>结果，严格遵循<动作决策>环节执行，得出动作决策"结果";
3.将动作决策结果，严格遵守<输出要求>输出最终结果，结果必须符合<输出格式>;
"""


def _parse_thought_content(thought_content: str) -> tuple:
    """
    从thought字段解析出五个字段

    Args:
        thought_content: thought字段内容，格式如：
        "界面分析: 界面顶部显示首页横幅...\n执行决策: 根据用例第一步...\n操作指令: 点击页面底部导航栏...\n执行动作: click"

    Returns:
        (界面分析, 步骤名称, 执行决策, 操作指令, 执行动作) 的元组
    """
    if not thought_content:
        return "", "", "", "", ""

    interface_analysis = ""
    current_step_name = ""
    action_decision = ""
    operation_instruction = ""
    action = ""

    # 按行分割内容
    lines = thought_content.split('\n')

    for line in lines:
        line = line.strip()
        if line.startswith("界面分析:") or line.startswith("界面分析："):
            interface_analysis = re.sub(r'^界面分析[:：]\s*', '', line)
        elif line.startswith("执行决策:") or line.startswith("执行决策："):
            action_decision = re.sub(r'^执行决策[:：]\s*', '', line)
        elif line.startswith("操作指令:") or line.startswith("操作指令："):
            operation_instruction = re.sub(r'^操作指令[:：]\s*', '', line)
        elif line.startswith("执行动作:") or line.startswith("执行动作："):
            action = re.sub(r'^执行动作[:：]\s*', '', line)

    return interface_analysis, current_step_name, action_decision, operation_instruction, action


def _parse_decision_content_new(decision_content: str) -> tuple:
    """
    从decision_content解析出四个字段（新格式）

    Args:
        decision_content: 决策内容字符串

    Returns:
        (步骤名称, 执行决策, 操作指令, 执行动作) 的元组
    """
    if not decision_content:
        return "", "", "", ""

    interface_analysis = ""
    current_step_name = ""
    action_decision = ""
    operation_instruction = ""
    action = ""

    # 尝试解析JSON格式
    try:
        if decision_content.strip().startswith('{'):
            data = json.loads(decision_content)
            interface_analysis = data.get("interface_analysis", "")
            current_step_name = data.get("current_step_name", "")
            action_decision = data.get("action_decision", "")
            operation_instruction = data.get("instruction", "")
            action = data.get("action", "")
            return interface_analysis, current_step_name, action_decision, operation_instruction, action
    except:
        pass

    # 使用正则表达式解析
    lines = decision_content.split('\n')
    for line in lines:
        line = line.strip()
        if re.match(r'.*界面分析.*[:：]', line):
            interface_analysis = re.sub(r'.*界面分析.*[:：]\s*', '', line)
        elif re.match(r'.*步骤名称.*[:：]', line):
            current_step_name = re.sub(r'.*步骤名称.*[:：]\s*', '', line)
        elif re.match(r'.*执行决策.*[:：]', line):
            action_decision = re.sub(r'.*执行决策.*[:：]\s*', '', line)
        elif re.match(r'.*操作指令.*[:：]', line):
            operation_instruction = re.sub(r'.*操作指令.*[:：]\s*', '', line)
        elif re.match(r'.*执行动作.*[:：]', line):
            action = re.sub(r'.*执行动作.*[:：]\s*', '', line)

    return interface_analysis, current_step_name, action_decision, operation_instruction, action


def _escape_template_variables(text: str) -> str:
    """
    转义文本中的模板变量符号，防止LangChain将其识别为变量

    Args:
        text: 原始文本

    Returns:
        转义后的文本
    """
    if not text:
        return text

    # 将单个花括号转义为双花括号
    # 这样LangChain就不会将其识别为模板变量
    return text.replace("{", "{{").replace("}", "}}")


def _build_combined_reference_actions_text(reference_actions: list, current_step_description: str,
                                           next_step_description: str = None, task_steps: list = None,
                                           current_step_index: int = 0) -> str:
    """
    构建组合的成功案例文本，按执行轮次顺序显示
    改为按顺序匹配，不再依赖步骤名称匹配

    Args:
        reference_actions: 参考动作列表
        current_step_description: 当前步骤描述（用于日志，实际匹配按顺序）
        next_step_description: 下一步骤描述（可选）
        task_steps: 任务步骤列表，用于匹配步骤名称序号
        current_step_index: 当前步骤索引

    Returns:
        格式化的组合成功案例文本
    """
    if not reference_actions:
        return ""

    # 按顺序分组动作：将所有动作按步骤顺序分组
    step_groups = _group_actions_by_step_order(reference_actions)

    # 收集当前步骤和下一步骤的所有成功案例
    all_relevant_actions = []

    # 添加当前步骤的所有成功案例（按顺序匹配）
    if current_step_index < len(step_groups):
        current_step_actions = step_groups[current_step_index]
        all_relevant_actions.extend(current_step_actions)

    # 添加下一步骤的第一轮成功案例（按顺序匹配）
    next_step_index = current_step_index + 1
    if next_step_index < len(step_groups):
        next_step_actions = step_groups[next_step_index]
        # 只添加下一步骤的第一轮
        if next_step_actions:
            all_relevant_actions.append(next_step_actions[0])

    if not all_relevant_actions:
        return "暂无相关成功案例"

    # 按轮次格式化所有成功案例
    actions_text = ""
    for i, action in enumerate(all_relevant_actions, 1):
        formatted_action = _format_execution_record_with_step_name(action, i, task_steps)
        if formatted_action:
            actions_text += formatted_action

    return actions_text


def _group_actions_by_step_order(actions: list) -> list:
    """
    将动作按步骤在成功案例中的出现顺序分组
    按照步骤在列表中的位置索引分组，而不是按步骤序号

    Args:
        actions: 所有动作记录列表

    Returns:
        按步骤出现顺序分组的动作列表，每个元素是一个步骤的所有动作
    """
    try:
        # 按照步骤名称的出现顺序分组
        seen_steps = []
        step_groups = []

        for action in actions:
            step_name = action.get("step_name", "")
            if not step_name:
                continue

            # 查找这个步骤名称是否已经出现过
            step_found = False
            for i, existing_step in enumerate(seen_steps):
                if existing_step == step_name:
                    # 如果已经存在，添加到对应的组中
                    step_groups[i].append(action)
                    step_found = True
                    break

            if not step_found:
                # 如果是新的步骤，创建新的组
                seen_steps.append(step_name)
                step_groups.append([action])

        return step_groups

    except Exception as e:
        # 如果出错，返回空列表
        return []


def _match_step_name_with_task_steps(step_name: str, task_steps: list) -> str:
    """
    将步骤名称与task_steps匹配，如果匹配成功就返回带序号的步骤名称

    Args:
        step_name: 原始步骤名称
        task_steps: 任务步骤列表

    Returns:
        匹配后的步骤名称（如果匹配成功则带序号，否则返回原名称）
    """
    if not step_name or not task_steps:
        return step_name

    # 去除原步骤名称中的序号（如果有的话）
    import re
    step_name_clean = re.sub(r'^\d+[.)、\-\s]*', '', step_name.strip())

    # 在task_steps中查找匹配的步骤
    for task_step in task_steps:
        # 去除task_step中的序号
        task_step_clean = re.sub(r'^\d+[.)、\-\s]*', '', task_step.strip())

        # 如果内容匹配，返回task_step（带序号）
        if step_name_clean == task_step_clean:
            return task_step

    # 如果没有匹配，返回原步骤名称
    return step_name


def _format_execution_record_with_step_name(record: dict, execution_count: int, task_steps: list = None) -> str:
    """
    格式化执行记录，包含步骤名称

    Args:
        record: 执行记录字典
        execution_count: 执行轮次
        task_steps: 任务步骤列表，用于匹配步骤名称序号

    Returns:
        格式化的执行记录文本
    """
    # 获取步骤名称
    step_name = record.get("step_name", "")

    # 尝试与task_steps匹配，如果匹配成功就使用带序号的步骤名称
    if step_name and task_steps:
        step_name = _match_step_name_with_task_steps(step_name, task_steps)

    # 尝试从parsed_fields获取结构化数据
    parsed_fields = record.get("parsed_fields", {})

    if parsed_fields:
        interface_analysis = parsed_fields.get("interface_analysis", "")
        action_decision = parsed_fields.get("action_decision", "")
        operation_instruction = parsed_fields.get("instruction", "")
        action = parsed_fields.get("action", "")
    else:
        # 从其他字段解析
        decision_content = record.get("decision_content", "")
        thought_content = record.get("thought", "")

        if thought_content:
            interface_analysis, _, action_decision, operation_instruction, action = _parse_thought_content(
                thought_content)
        else:
            interface_analysis, _, action_decision, operation_instruction, action = _parse_decision_content_new(
                decision_content)

    # 如果连步骤名称和动作都没有，则跳过这条记录
    if not step_name and not action:
        return ""

    # 构建格式化文本
    formatted_text = f"""**第{execution_count}轮执行**
- 步骤名称: {step_name}
- 界面分析: {interface_analysis}
- 决策内容: {action_decision}
- 操作指令: {operation_instruction}
- 执行动作: {action}
"""

    return _escape_template_variables(formatted_text)


def _is_step_match(action_step_name: str, current_step_description: str) -> bool:
    """
    判断动作步骤名称是否与当前步骤描述匹配
    使用简单的字符串匹配逻辑

    Args:
        action_step_name: 动作中的步骤名称
        current_step_description: 当前步骤描述

    Returns:
        是否匹配
    """
    if not action_step_name or not current_step_description:
        return False

    # 清理字符串
    action_name = action_step_name.strip()
    current_desc = current_step_description.strip()

    # 1. 完全匹配
    if action_name == current_desc:
        return True

    # 2. 去除序号后完全匹配
    action_name_no_num = re.sub(r'^\d+[.)、\-\s]*', '', action_name)
    current_desc_no_num = re.sub(r'^\d+[.)、\-\s]*', '', current_desc)

    if action_name_no_num == current_desc_no_num:
        return True

    # 3. 标准化后匹配（去除引号、括号等格式差异）
    action_normalized = _normalize_step_text(action_name_no_num)
    current_normalized = _normalize_step_text(current_desc_no_num)

    if action_normalized == current_normalized:
        return True

    return False


def _normalize_step_text(text: str) -> str:
    """
    标准化步骤文本，去除格式差异

    Args:
        text: 原始文本

    Returns:
        标准化后的文本
    """
    if not text:
        return ""

    # 去除各种引号和括号
    normalized = re.sub(r"['\"""（）()]", "", text)
    # 去除斜杠和特殊符号
    normalized = re.sub(r"[/\\]", "", normalized)
    # 去除多余空格
    normalized = re.sub(r'\s+', '', normalized)
    # 转换为小写
    normalized = normalized.lower()

    return normalized


def _build_combined_execution_history(history: list) -> str:
    """
    构建组合执行历史，包含所有执行记录

    Args:
        history: 执行历史列表

    Returns:
        格式化的组合执行历史文本
    """
    if not history:
        return "暂无执行历史"

    # 获取所有step_execution_with_reference的执行记录
    relevant_history = [r for r in history if r.get("action") == "step_execution_with_reference"]

    if not relevant_history:
        return "暂无执行历史"

    # 按时间戳排序
    relevant_history.sort(key=lambda x: x.get("timestamp", ""))

    combined_content = "**以下为你本次回归测试的执行记忆内容**\n"
    execution_round = 0

    # 直接循环追加所有执行记录
    for record in relevant_history:
        ai_response = record.get("ai_response", "")
        action_command = record.get("action_command", "")
        step_description = record.get("step_description", "")

        # 直接解析JSON，因为模型输出都是JSON格式
        try:
            # 清理JSON字符串，移除可能的前后空白和特殊字符
            cleaned_response = ai_response.strip()

            # 如果不是以{开头和}结尾，尝试提取JSON部分
            if not (cleaned_response.startswith('{') and cleaned_response.endswith('}')):
                # 尝试找到JSON部分
                start_idx = cleaned_response.find('{')
                end_idx = cleaned_response.rfind('}')
                if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
                    cleaned_response = cleaned_response[start_idx:end_idx + 1]

            data = json.loads(cleaned_response)
            action_decision = data.get("action_decision", "")

            # 直接追加，不做复杂的验证
            execution_round += 1
            formatted_text = f"""**第{execution_round}轮执行**
- 步骤名称: {step_description}
- 决策内容: {action_decision}
- 执行动作: {action_command}
"""
            combined_content += _escape_template_variables(formatted_text)

        except (json.JSONDecodeError, Exception) as e:
            action_decision = ""

            if '"action_decision"' in ai_response:
                try:
                    start = ai_response.find('"action_decision"') + len('"action_decision"')
                    start = ai_response.find('"', start) + 1
                    end = ai_response.find('",', start)
                    if end == -1:
                        end = ai_response.find('"', start)
                    if start > 0 and end > start:
                        action_decision = ai_response[start:end]
                except:
                    pass

            execution_round += 1
            formatted_text = f"""**第{execution_round}轮执行**
- 步骤名称: {step_description}
- 决策内容: {action_decision if action_decision else '[解析失败]'}
- 执行动作: {action_command}
"""
            combined_content += _escape_template_variables(formatted_text)

    return combined_content if execution_round > 0 else "暂无执行历史"


def get_reference_exception_handling() -> str:
    """获取参考任务特殊场景处理策略"""
    return """* 如界面出现弹窗，且弹窗带倒计时，则调用wait(seconds=倒计时秒数)等待倒计时结束自动消失；
* 如界面出现页面加载、动画、广告遮挡目标元素，则调用wait(seconds=3)等待加载完成或广告消失；
* 如界面出现弹窗，且弹窗不带倒计时，但附近存在'我知道了'、'同意'、'取消' 'X'等按钮，则调用click点击按钮关闭弹窗；
* 如界面出现弹窗，且弹窗不带倒计时，也不带任何可点击关闭的按钮，那么使用back尝试关闭弹窗；
* 如界面边缘存在悬浮气泡遮挡了目标元素位置，可以先通过拖动(drag)上/下移动悬浮气泡，露出目标元素；
* 如界面出现未加载完成、空白页面、页面切换中、异常页面，则调用wait(seconds=3)等待加载完成，其中若'空白页'和'异常页'出现连续多次(>2次)等待则考虑系统问题；
**特别注意**：弹窗处理优先级最高，必须确保弹窗处理完毕后，再进行其他操作"""
